/**
 * AI-Driven Contextual Analysis System
 * Replaces score-based logic with intelligent, website-specific insights
 */

import { loggedFetch, log } from './logger';
import type { Database } from '../types/supabase';
import { mockAIResponses, useMockAI } from './mock-ai-service';

type Analysis = Database['public']['Tables']['analyses']['Row'];

const OPENROUTER_API_KEY = import.meta.env.PUBLIC_OPENROUTER_API_KEY;
const OPENROUTER_BASE_URL = 'https://openrouter.ai/api/v1';

if (!OPENROUTER_API_KEY) {
  throw new Error('Missing PUBLIC_OPENROUTER_API_KEY environment variable');
}

const activeModel = 'qwen/qwen-2.5-72b-instruct:free';

export interface WebsiteContext {
  url: string;
  htmlContent: string;
  title: string;
  metaDescription?: string;
  headings: string[];
  images: string[];
  businessType: string;
  targetAudience: string;
  valueProposition: string;
  performanceData: any;
  seoData: any;
  lighthouseData: any;
  conversionElements: any;
}

export interface AIInsight {
  title: string; // ≤10 words
  description: string;
  evidence: string;
  impactExplanation: string;
  implementationSteps: string;
  businessValue: string;
  priorityScore: number; // 0-100
  confidenceScore: number; // 0-100
  category: string;
  insightType: 'strength' | 'weakness' | 'opportunity' | 'recommendation';
}

export interface AILeadInsight {
  questionType: 'qualification' | 'concern' | 'business' | 'objection';
  questionText: string;
  contextExplanation: string;
  qualificationValue: string;
  suggestedResponse: string;
  priorityLevel: number; // 1-5
}

export interface AIPageSummary {
  pagePurpose: string;
  keyTakeaways: string[];
  heroEffectiveness: string;
  valuePropositionClarity: string;
  leadCaptureAssessment: string;
  userJourneyAnalysis: string;
  businessImpactSummary: string;
}

export interface AIPerformanceInsight {
  primaryIssue: {
    title: string;
    description: string;
    evidence: string;
    conversionImpact: string;
  };
  keyFindings: Array<{
    metric: string;
    currentValue: string;
    targetValue: string;
    impact: string;
  }>;
  recommendations: Array<{
    title: string;
    description: string;
    expectedImprovement: string;
    priority: string;
  }>;
  businessImpact: string;
}

/**
 * Extract comprehensive website context from analysis data
 */
export function extractWebsiteContext(analysis: Analysis): WebsiteContext {
  let lighthouseData = null;
  if (analysis.lighthouse_data) {
    log.debug(`Raw lighthouse_data: ${analysis.lighthouse_data}`);
    try {
      lighthouseData = typeof analysis.lighthouse_data === 'string' ? 
        JSON.parse(analysis.lighthouse_data) : 
        analysis.lighthouse_data;
    } catch (e) {
      log.error(`Failed to parse lighthouse_data: ${e}`);
    }
  }

  let seoData = null;
  if (analysis.seo_data) {
    log.debug(`Raw seo_data: ${analysis.seo_data}`);
    try {
      seoData = typeof analysis.seo_data === 'string' ? 
        JSON.parse(analysis.seo_data) : 
        analysis.seo_data;
    } catch (e) {
      log.error(`Failed to parse seo_data: ${e}`);
    }
  }

  const scrapedData = lighthouseData?.scrapedData || {};

  return {
    url: analysis.url || '',
    htmlContent: scrapedData.content || '',
    title: analysis.title || scrapedData.title || '',
    metaDescription: scrapedData.metadata?.description,
    headings: scrapedData.headings || [],
    images: scrapedData.images || [],
    businessType: 'Unknown',
    targetAudience: 'General audience',
    valueProposition: 'Value proposition not identified',
    performanceData: {
      performanceScore: analysis.performance_score,
      lcpScore: analysis.lcp_score,
      fidScore: analysis.fid_score,
      clsScore: analysis.cls_score,
      performanceGrade: analysis.performance_grade
    },
    seoData: seoData,
    lighthouseData: lighthouseData,
    conversionElements: scrapedData.conversionElements || {}
  };
}

/**
 * Generate AI-powered pros and cons insights based on actual website content
 */
export async function generateAIProsConsInsights(analysis: Analysis): Promise<{
  strengths: AIInsight[];
  weaknesses: AIInsight[];
  overallAssessment: string;
}> {
  const context = extractWebsiteContext(analysis);
  
  const prompt = `You are an expert conversion rate optimization consultant. Analyze this website and provide specific, actionable pros and cons based on the actual content and structure.

WEBSITE CONTEXT:
- URL: ${context.url}
- Title: ${context.title}
- Business Type: ${context.businessType}
- Target Audience: ${context.targetAudience}
- Performance Score: ${context.performanceData.performanceScore || 'Not available'}
- SEO Score: ${analysis.seo_score || 'Not available'}

ACTUAL CONTENT ANALYSIS:
${context.htmlContent.substring(0, 3000)}...

HEADINGS STRUCTURE:
${context.headings.slice(0, 8).join(' | ')}

PERFORMANCE METRICS:
- LCP: ${context.performanceData.lcpScore || 'N/A'}s
- FID: ${context.performanceData.fidScore || 'N/A'}ms  
- CLS: ${context.performanceData.clsScore || 'N/A'}

CONVERSION ELEMENTS DETECTED:
- Forms: ${context.conversionElements.forms_detected ? 'Yes' : 'No'}
- CTA Buttons: ${context.conversionElements.cta_buttons || 0}
- Trust Signals: ${context.conversionElements.trust_signals ? 'Yes' : 'No'}
- Pricing Visible: ${context.conversionElements.pricing_visible ? 'Yes' : 'No'}

INSTRUCTIONS:
1. Analyze the ACTUAL website content, not just scores
2. Identify specific strengths based on detected elements (forms, CTAs, content quality, etc.)
3. Identify specific weaknesses based on missing elements or poor implementation
4. Each insight should reference actual page elements or detected issues
5. Provide actionable recommendations with specific implementation steps
6. Focus on conversion optimization impact for this specific business type
7. Generate unique insights for each website - no generic templates

Return a JSON object with this structure:
{
  "strengths": [
    {
      "title": "Concise strength header (≤10 words)",
      "description": "Detailed explanation of what works well",
      "evidence": "Specific element or content observed on this website",
      "impactExplanation": "How this helps conversions for this business type",
      "businessValue": "Business impact of maintaining this strength",
      "priorityScore": 85,
      "confidenceScore": 90,
      "category": "conversion"
    }
  ],
  "weaknesses": [
    {
      "title": "Concise weakness header (≤10 words)",
      "description": "Detailed explanation of the issue",
      "evidence": "Specific missing element or poor implementation observed",
      "impactExplanation": "How this hurts conversions for this business type",
      "implementationSteps": "Step-by-step instructions to fix this issue",
      "businessValue": "Business impact of fixing this weakness",
      "priorityScore": 75,
      "confidenceScore": 85,
      "category": "conversion"
    }
  ],
  "overallAssessment": "Brief summary of the page's conversion potential based on actual analysis"
}

Limit to 6 strengths and 6 weaknesses maximum. Be specific and reference actual page elements. Make each insight unique to this specific website.`;

  try {
    log.info(`Generating AI pros/cons insights for ${context.url}`);
    
    const response = await loggedFetch(`${OPENROUTER_BASE_URL}/chat/completions`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${OPENROUTER_API_KEY}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: activeModel,
        messages: [
          {
            role: "system",
            content: "You are an expert conversion rate optimization consultant. Analyze websites based on actual content and provide specific, actionable insights that are unique to each website."
          },
          {
            role: "user",
            content: prompt
          }
        ],
        response_format: { type: "json_object" },
        max_tokens: 3000,
      })
    });

    const responseText = await response.text();
    log.info(`OpenRouter raw response for pros/cons: ${responseText}`);

    const data = JSON.parse(responseText);
    if (data.error) throw new Error(data.error.message);
    
    const result = JSON.parse(data.choices[0]?.message?.content || '{}');
    log.info(`AI pros/cons insights generated for ${context.url}`);
    
    return {
      strengths: result.strengths || [],
      weaknesses: result.weaknesses || [],
      overallAssessment: result.overallAssessment || 'Analysis completed based on website content'
    };
  } catch (error) {
    log.error(`Error generating AI pros/cons insights: ${error}`);
    if (error instanceof SyntaxError) {
        log.error("Failed to parse JSON response from AI.");
    }
    throw error;
  }
}

/**
 * Generate AI-powered lead qualification insights
 */
export async function generateAILeadInsights(analysis: Analysis): Promise<{
  businessType: string;
  valueProposition: string;
  targetAudience: string;
  targetAudienceMarkdown?: string;
  businessTypeDescription?: string;
  valuePropositionDescription?: string;
  leadQuestions: AILeadInsight[];
  businessImpact: string[];
  conversionBarriers: Array<{
    barrier: string;
    evidence: string;
    solution: string;
  }>;
}> {
  const context = extractWebsiteContext(analysis);

  const prompt = `You are a lead generation and sales psychology expert. Analyze this actual website content to identify realistic questions, concerns, and objections that prospects would have after visiting THIS specific page.

WEBSITE CONTEXT:
- URL: ${context.url}
- Title: ${context.title}
- Business Type (initial): ${context.businessType}
- Target Audience (initial): ${context.targetAudience}

ACTUAL PAGE CONTENT:
${context.htmlContent.substring(0, 4000)}...

HEADINGS STRUCTURE:
${context.headings.join(' | ')}

DETECTED ELEMENTS:
- Images: ${context.images.length} found
- Forms: ${context.htmlContent.includes('<form') ? 'Contact/signup forms detected' : 'No forms detected'}
- Pricing: ${context.htmlContent.toLowerCase().includes('price') || context.htmlContent.toLowerCase().includes('$') ? 'Pricing information present' : 'No pricing information visible'}
- Trust Signals: ${context.conversionElements.trust_signals ? 'Testimonials/reviews detected' : 'No trust signals detected'}
- Contact Info: ${context.conversionElements.contact_info ? 'Contact information present' : 'No contact information visible'}

INSTRUCTIONS:
1. Analyze the ACTUAL content, value proposition, and messaging on this specific page
2. Identify the specific business type and target audience from content analysis
3. Generate realistic questions prospects would have after reading THIS specific page content
4. Identify concerns based on what's missing or unclear in THIS page's content
5. Consider the user journey and what information gaps exist on THIS page
6. Focus on lead qualification and sales enablement for THIS specific business
7. Generate unique insights - no generic templates

Return a JSON object with this structure:
{
  "businessType": "Specific business type identified from this page's content analysis",
  "businessTypeDescription": "1-2 sentences describing the business context (not just the label)",
  "valueProposition": "Main value proposition extracted from this page",
  "valuePropositionDescription": "1-2 sentences that expand on the value proposition",
  "targetAudience": "Specific target audience identified from this page's content and messaging",
  "targetAudienceMarkdown": "Markdown with 2-3 detailed personas (headings, bullets). Use only plain Markdown, no HTML.",
  "leadQuestions": [
    {
      "questionType": "qualification",
      "questionText": "Specific question a prospect would ask after visiting THIS page",
      "contextExplanation": "Why this question arises from THIS page's content",
      "qualificationValue": "How this question helps qualify leads for THIS business",
      "suggestedResponse": "How to address this question for THIS specific business",
      "priorityLevel": 4
    }
  ],
  "businessImpact": [
    "Specific business impact or opportunity identified from THIS page's analysis"
  ],
  "conversionBarriers": [
    {
      "barrier": "Specific element preventing conversion on THIS page",
      "evidence": "What you observed in THIS page's content",
      "solution": "Specific recommendation to remove this barrier from THIS page"
    }
  ]
}

Generate 8-12 lead questions across different types. Be specific to THIS website's actual content and business model. Make each insight unique to this specific page.`;

  try {
    log.info(`Generating AI lead insights for ${context.url}`);

    const response = await loggedFetch(`${OPENROUTER_BASE_URL}/chat/completions`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${OPENROUTER_API_KEY}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: activeModel,
        messages: [
          {
            role: "system",
            content: "You are a lead generation and sales psychology expert. Analyze actual website content to identify realistic prospect questions and concerns that are unique to each specific website."
          },
          {
            role: "user",
            content: prompt
          }
        ],
        response_format: { type: "json_object" },
        max_tokens: 3000,
      })
    });

    const data = await response.json();
    if (data.error) throw new Error(data.error.message);

    const result = JSON.parse(data.choices[0]?.message?.content || '{}');
    log.info(`AI lead insights generated for ${context.url}`);

    return {
      businessType: result.businessType || context.businessType,
      valueProposition: result.valueProposition || context.valueProposition,
      targetAudience: result.targetAudience || context.targetAudience,
      targetAudienceMarkdown: result.targetAudienceMarkdown || '',
      businessTypeDescription: result.businessTypeDescription || '',
      valuePropositionDescription: result.valuePropositionDescription || '',
      leadQuestions: result.leadQuestions || [],
      businessImpact: result.businessImpact || [],
      conversionBarriers: result.conversionBarriers || []
    };
  } catch (error) {
    log.error(`Error generating AI lead insights: ${error}`);
    throw error;
  }
}

/**
 * Generate AI-powered page summary for lead insights
 */
export async function generateAIPageSummary(analysis: Analysis): Promise<AIPageSummary> {
  const context = extractWebsiteContext(analysis);
  
  const prompt = `You are a UX and content strategy expert. Analyze this actual website page to create a comprehensive summary focused on lead generation and conversion effectiveness.

WEBSITE CONTEXT:
- URL: ${context.url}
- Title: ${context.title}
- Business Type: ${context.businessType}
- Target Audience: ${context.targetAudience}

ACTUAL PAGE CONTENT:
${context.htmlContent.substring(0, 4000)}...

CONTENT STRUCTURE:
- Main Headings: ${context.headings.slice(0, 8).join(' | ')}
- Images: ${context.images.length} detected
- Forms: ${context.htmlContent.includes('<form') ? 'Contact/signup forms detected' : 'No forms detected'}
- Trust Signals: ${context.conversionElements.trust_signals ? 'Present' : 'Missing'}
- Pricing Info: ${context.conversionElements.pricing_visible ? 'Visible' : 'Not visible'}

INSTRUCTIONS:
1. Analyze the ACTUAL page content and structure of THIS specific website
2. Assess how effectively THIS page communicates value to prospects
3. Evaluate THIS page's hero section hook and clarity
4. Identify what leads would understand and remember from THIS specific page
5. Assess THIS page's lead capture mechanisms and user journey
6. Generate unique insights for THIS website - no generic templates

Return a JSON object with this structure:
{
  "pagePurpose": "Clear statement of what THIS specific page is designed to accomplish",
  "keyTakeaways": [
    "What visitors would remember after reading THIS page",
    "Main benefits or value propositions they'd understand from THIS page",
    "Key differentiators that would stick with them from THIS page"
  ],
  "heroEffectiveness": "Assessment of how well THIS page's hero section hooks visitors and communicates value",
  "valuePropositionClarity": "How clearly THIS page communicates its unique value proposition",
  "leadCaptureAssessment": "Analysis of THIS page's lead capture mechanisms and their effectiveness",
  "userJourneyAnalysis": "How well THIS page's content guides users toward conversion",
  "businessImpactSummary": "Overall business impact assessment for THIS specific page"
}

Base all analysis on THIS page's actual content, not assumptions. Make insights unique to this specific website.`;

  try {
    log.info(`Generating AI page summary for ${context.url}`);
    
    const response = await loggedFetch(`${OPENROUTER_BASE_URL}/chat/completions`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${OPENROUTER_API_KEY}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: activeModel,
        messages: [
          {
            role: "system",
            content: "You are a UX and content strategy expert. Analyze actual website pages to create comprehensive summaries focused on lead generation effectiveness. Generate unique insights for each website."
          },
          {
            role: "user",
            content: prompt
          }
        ],
        response_format: { type: "json_object" },
        max_tokens: 2000,
      })
    });

    const data = await response.json();
    if (data.error) throw new Error(data.error.message);
    
    const result = JSON.parse(data.choices[0]?.message?.content || '{}');
    log.info(`AI page summary generated for ${context.url}`);
    
    return {
      pagePurpose: result.pagePurpose || 'Page purpose analysis completed',
      keyTakeaways: result.keyTakeaways || [],
      heroEffectiveness: result.heroEffectiveness || 'Hero section analysis completed',
      valuePropositionClarity: result.valuePropositionClarity || 'Value proposition analysis completed',
      leadCaptureAssessment: result.leadCaptureAssessment || 'Lead capture analysis completed',
      userJourneyAnalysis: result.userJourneyAnalysis || 'User journey analysis completed',
      businessImpactSummary: result.businessImpactSummary || 'Business impact analysis completed'
    };
  } catch (error) {
    log.error(`Error generating AI page summary: ${error}`);
    throw error;
  }
}

/**
 * Generate AI-powered performance impact analysis
 */
export async function generateAIPerformanceInsights(analysis: Analysis): Promise<AIPerformanceInsight> {
  const context = extractWebsiteContext(analysis);
  
  const prompt = `You are a web performance expert specializing in Core Web Vitals and conversion optimization. Analyze the actual performance data to provide specific, actionable insights for THIS website.

WEBSITE CONTEXT:
- URL: ${context.url}
- Business Type: ${context.businessType}
- Target Audience: ${context.targetAudience}

ACTUAL PERFORMANCE DATA:
- Performance Score: ${context.performanceData.performanceScore || 'Not available'}/100
- LCP: ${context.performanceData.lcpScore || 'N/A'}s
- FID: ${context.performanceData.fidScore || 'N/A'}ms
- CLS: ${context.performanceData.clsScore || 'N/A'}
- Grade: ${context.performanceData.performanceGrade || 'Not graded'}

LIGHTHOUSE AUDIT RESULTS:
${context.lighthouseData ? JSON.stringify(context.lighthouseData, null, 2).substring(0, 2000) : 'Lighthouse data not available'}

HTML CONTENT (for context):
${context.htmlContent.substring(0, 2000)}...

INSTRUCTIONS:
1. Analyze the ACTUAL Lighthouse audit results and performance metrics for THIS website
2. Identify specific performance bottlenecks from THIS website's audit data
3. Calculate realistic conversion impact based on actual metrics for THIS business type
4. Provide specific, implementable recommendations for THIS website
5. Reference actual detected issues (large images, render-blocking resources, etc.) from THIS audit
6. Generate unique insights for THIS website - no generic advice

Return a JSON object with this structure:
{
  "primaryIssue": {
    "title": "Concise header (≤10 words) describing the main performance problem on THIS website",
    "description": "Detailed explanation of the specific issue found in THIS website's audit",
    "evidence": "Specific metrics or audit findings from THIS website that support this",
    "conversionImpact": "Realistic estimate of conversion impact with reasoning for THIS business type"
  },
  "keyFindings": [
    {
      "metric": "Specific Core Web Vital or performance metric from THIS audit",
      "currentValue": "Actual measured value from THIS website",
      "targetValue": "Recommended target for THIS business type",
      "impact": "How this affects user experience and conversions for THIS website"
    }
  ],
  "recommendations": [
    {
      "title": "Specific, actionable recommendation header for THIS website (≤10 words)",
      "description": "Detailed implementation steps for THIS website",
      "expectedImprovement": "Realistic performance gain for THIS website",
      "priority": "High/Medium/Low based on THIS website's specific needs"
    }
  ],
  "businessImpact": "Overall assessment of how performance affects THIS specific website's business goals"
}

Focus on actionable insights based on THIS website's actual audit data, not generic performance advice.`;

  try {
    log.info(`Generating AI performance insights for ${context.url}`);
    
    const response = await loggedFetch(`${OPENROUTER_BASE_URL}/chat/completions`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${OPENROUTER_API_KEY}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: activeModel,
        messages: [
          {
            role: "system",
            content: "You are a web performance expert specializing in Core Web Vitals and conversion optimization. Analyze actual performance data to provide specific insights unique to each website."
          },
          {
            role: "user",
            content: prompt
          }
        ],
        response_format: { type: "json_object" },
        max_tokens: 3000,
      })
    });

    const data = await response.json();
    if (data.error) throw new Error(data.error.message);
    
    const result = JSON.parse(data.choices[0]?.message?.content || '{}');
    log.info(`AI performance insights generated for ${context.url}`);
    
    return {
      primaryIssue: result.primaryIssue || {
        title: 'Performance Analysis Complete',
        description: 'Performance metrics have been analyzed',
        evidence: 'Based on available data',
        conversionImpact: 'Impact assessment completed'
      },
      keyFindings: result.keyFindings || [],
      recommendations: result.recommendations || [],
      businessImpact: result.businessImpact || 'Performance optimization recommendations provided'
    };
  } catch (error) {
    log.error(`Error generating AI performance insights: ${error}`);
    throw error;
  }
}

/**
 * Generate AI-powered performance & SEO summary
 */
export async function generateAIPerformanceSEOSummary(analysis: Analysis): Promise<{
  executiveSummary: string;
  keyFindings: string[];
  businessImpact: string;
  actionPlan: Array<{
    phase: string;
    title: string;
    description: string;
    timeline: string;
    expectedImpact: string;
  }>;
  metaOptimizationNotes?: string;
  contentSeoAssessment?: string;
  technicalSeoIssues?: string[];
  keywordOpportunities?: string[];
  localSeoRecommendations?: string;
  schemaMarkupSuggestions?: string[];
  internalLinkingAnalysis?: string;
  pageSpeedSeoImpact?: string;
  mobileSeoAssessment?: string;
  competitorAnalysisNotes?: string;
  contentGapAnalysis?: string[];
  seoPriorityActions?: string[];
  expectedTrafficImpact?: string;
}> {
  const context = extractWebsiteContext(analysis);
  
  const prompt = `You are a technical SEO and web performance expert. Analyze the actual audit data to provide specific, actionable insights for THIS website's business impact.

WEBSITE CONTEXT:
- URL: ${context.url}
- Business Type: ${context.businessType}
- Target Audience: ${context.targetAudience}

PERFORMANCE DATA:
- Performance Score: ${context.performanceData.performanceScore || 'Not available'}/100
- LCP: ${context.performanceData.lcpScore || 'N/A'}s
- FID: ${context.performanceData.fidScore || 'N/A'}ms
- CLS: ${context.performanceData.clsScore || 'N/A'}

SEO DATA:
${context.seoData ? JSON.stringify(context.seoData, null, 2).substring(0, 1500) : 'SEO data not available'}

ACTUAL PAGE CONTENT:
${context.htmlContent.substring(0, 2000)}...

INSTRUCTIONS:
1. Analyze ACTUAL audit results and detected issues from THIS website
2. Identify specific technical problems from THIS website's data
3. Provide actionable recommendations with implementation details for THIS website
4. Calculate realistic business impact based on actual metrics for THIS business type
5. Create a phased action plan with realistic timelines for THIS website
6. Generate unique insights for THIS website - no generic templates

Return a JSON object with this structure:
{
  "executiveSummary": "High-level assessment based on actual audit results for THIS specific website and business type",
  "keyFindings": [
    "Most important technical findings that affect THIS specific business",
    "Specific performance or SEO issues detected on THIS website",
    "Opportunities for improvement with highest business impact for THIS website"
  ],
  "businessImpact": "How performance and SEO issues specifically affect THIS website's goals and revenue",
  "actionPlan": [
    {
      "phase": "Immediate (0-2 weeks)",
      "title": "Quick technical wins for THIS website",
      "description": "Specific actions to take immediately on THIS website",
      "timeline": "1-2 weeks",
      "expectedImpact": "Realistic improvement estimate for THIS website"
    },
    {
      "phase": "Short-term (1-3 months)",
      "title": "Strategic improvements for THIS website",
      "description": "Medium-effort optimizations for THIS website",
      "timeline": "4-12 weeks", 
      "expectedImpact": "Realistic improvement estimate for THIS website"
    },
    {
      "phase": "Long-term (3-6 months)",
      "title": "Advanced optimizations for THIS website",
      "description": "Complex technical enhancements for THIS website",
      "timeline": "3-6 months",
      "expectedImpact": "Realistic improvement estimate for THIS website"
    }
  ]
}

Focus on specific, implementable recommendations based on THIS website's actual audit findings.`;

  try {
    log.info(`Generating AI performance & SEO summary for ${context.url}`);
    
    const response = await loggedFetch(`${OPENROUTER_BASE_URL}/chat/completions`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${OPENROUTER_API_KEY}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: activeModel,
        messages: [
          {
            role: "system",
            content: "You are a technical SEO and web performance expert. Analyze actual audit data to provide specific, actionable insights unique to each website."
          },
          {
            role: "user",
            content: prompt
          }
        ],
        response_format: { type: "json_object" },
        max_tokens: 2500,
      })
    });

    const data = await response.json();
    if (data.error) throw new Error(data.error.message);

    const result = JSON.parse(data.choices[0]?.message?.content || '{}');
    log.info(`AI performance & SEO summary generated for ${context.url}`);

    return {
      executiveSummary: result.executiveSummary || 'Performance and SEO analysis completed',
      keyFindings: result.keyFindings || [],
      businessImpact: result.businessImpact || 'Performance and SEO improvements can impact business goals',
      actionPlan: result.actionPlan || [],
      metaOptimizationNotes: result.metaOptimizationNotes || '',
      contentSeoAssessment: result.contentSeoAssessment || '',
      technicalSeoIssues: result.technicalSeoIssues || [],
      keywordOpportunities: result.keywordOpportunities || [],
      localSeoRecommendations: result.localSeoRecommendations || '',
      schemaMarkupSuggestions: result.schemaMarkupSuggestions || [],
      internalLinkingAnalysis: result.internalLinkingAnalysis || '',
      pageSpeedSeoImpact: result.pageSpeedSeoImpact || '',
      mobileSeoAssessment: result.mobileSeoAssessment || '',
      competitorAnalysisNotes: result.competitorAnalysisNotes || '',
      contentGapAnalysis: result.contentGapAnalysis || [],
      seoPriorityActions: result.seoPriorityActions || [],
      expectedTrafficImpact: result.expectedTrafficImpact || ''
    };
  } catch (error) {
    log.error(`Error generating AI performance & SEO summary: ${error}`);
    throw error;
  }
}