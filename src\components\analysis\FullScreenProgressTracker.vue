<script setup lang="ts">
import { ref, onMounted, onUnmounted, computed } from 'vue';
import { CheckCircle, Clock, AlertCircle, Loader2, Zap, FileText, BarChart3, Users, Search, MessageCircle, Target, Settings, Scale, TrendingUp, MessageSquare } from 'lucide-vue-next';
import {
  PROGRESS_STEPS,
  TOTAL_STEPS,
  getProgressStep,
  getProgressPercentage,
  getEstimatedTimeRemaining,
  formatTimeRemaining,
  getStepStatus
} from '../../lib/progress-steps';

interface ProgressStep {
  stepNumber: number;
  stepName: string;
  description: string;
  status: 'pending' | 'in_progress' | 'completed' | 'failed';
  startedAt?: string;
  completedAt?: string;
  errorMessage?: string;
  isActive: boolean;
  isCompleted: boolean;
  isFailed: boolean;
}

interface AnalysisProgress {
  analysisId: string;
  status: 'pending' | 'in_progress' | 'completed' | 'failed';
  statusMessage: string;
  currentStep: number; // Changed from string to number
  progressPercentage: number;
  completedSteps: number;
  totalSteps: number;
  isComplete: boolean;
  hasError: boolean;
  startedAt?: string;
  completedAt?: string;
  errorMessage?: string;
  steps: ProgressStep[];
  lastUpdated: string;
}

const props = defineProps<{
  analysisId: string;
  websiteUrl: string;
  onComplete?: () => void;
}>();

const emit = defineEmits<{
  complete: [];
  error: [string];
}>();

const progress = ref<AnalysisProgress | null>(null);
const isLoading = ref(true);
const error = ref<string | null>(null);
const pollInterval = ref<number | null>(null);
const isVisible = ref(true);

const stepIcons = {
  1: Search,
  2: FileText,
  3: BarChart3,
  4: Users,
  5: Zap,
  6: Search,
  7: MessageCircle,
  8: Target,
  9: CheckCircle
};

const fetchProgress = async () => {
  try {
    // Use fetch with no-cache headers for immediate updates
    const response = await fetch(`/api/analysis-progress?analysisId=${props.analysisId}`, {
      method: 'GET',
      headers: {
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0'
      }
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const data = await response.json();

    if (data.error) {
      throw new Error(data.error);
    }

    // Generate steps from static definitions and current progress
    const generatedSteps = PROGRESS_STEPS.map(stepDef => {
      const status = getStepStatus(stepDef.id, data.currentStep || 0, data.isComplete || false);
      return {
        stepNumber: stepDef.id,
        stepName: stepDef.name,
        description: stepDef.description,
        status,
        isActive: status === 'in_progress',
        isCompleted: status === 'completed',
        isFailed: false,
        startedAt: status !== 'pending' ? new Date().toISOString() : undefined,
        completedAt: status === 'completed' ? new Date().toISOString() : undefined
      };
    });

    // Calculate progress percentage from current step
    const calculatedPercentage = getProgressPercentage(data.currentStep || 0, data.isComplete || false);

    // Enhanced progress data handling
    progress.value = {
      ...data,
      steps: generatedSteps,
      progressPercentage: calculatedPercentage,
      totalSteps: TOTAL_STEPS,
      completedSteps: Math.max(0, (data.currentStep || 1) - 1),
      statusMessage: data.isComplete ? 'Analysis Complete!' :
                    (getProgressStep(data.currentStep || 1)?.name || 'Processing...'),
      // Ensure immediate display of progress
      isVisible: true,
      lastFetched: Date.now()
    };
    error.value = null;

    // Log progress for debugging
    console.log(`Progress update: ${data.progressPercentage}% - ${data.statusMessage}`);

    // If analysis is complete or failed, trigger completion
    if (data.isComplete || data.hasError) {
      stopPolling();

      // Wait a moment to show completion, then trigger fade out
      setTimeout(() => {
        if (data.isComplete) {
          emit('complete');

          // Emit custom DOM event for parent page to listen to
          const event = new CustomEvent('analysis-complete', {
            detail: { analysisId: props.analysisId }
          });
          document.dispatchEvent(event);

          // Hide all progress components smoothly
          hideAllProgressComponents();

          if (props.onComplete) {
            props.onComplete();
          }
        } else {
          emit('error', data.errorMessage || 'Analysis failed');

          // Emit custom DOM event for error
          const errorEvent = new CustomEvent('analysis-error', {
            detail: {
              analysisId: props.analysisId,
              error: data.errorMessage || 'Analysis failed'
            }
          });
          document.dispatchEvent(errorEvent);
        }
      }, 1500); // Increased delay to show completion state longer
    }
  } catch (err) {
    console.error('Error fetching progress:', err);
    error.value = err instanceof Error ? err.message : 'Failed to fetch progress';

    // Enhanced error handling with exponential backoff
    if (error.value.includes('404') || error.value.includes('403')) {
      stopPolling();
      emit('error', error.value);
    } else {
      // Implement exponential backoff for network errors
      const retryCount = (progress.value?.retryCount || 0) + 1;
      const maxRetries = 5;

      if (retryCount <= maxRetries) {
        const backoffDelay = Math.min(30000, 1000 * Math.pow(2, retryCount)); // Max 30 seconds

        console.warn(`Progress fetch failed (attempt ${retryCount}/${maxRetries}), retrying in ${backoffDelay}ms`);

        // Update progress with retry info
        if (progress.value) {
          progress.value.retryCount = retryCount;
          progress.value.lastError = error.value;
          progress.value.nextRetryAt = Date.now() + backoffDelay;
        }

        setTimeout(() => {
          if (!progress.value?.isComplete && retryCount <= maxRetries) {
            fetchProgress();
          }
        }, backoffDelay);
      } else {
        // Max retries exceeded
        console.error('Max retries exceeded for progress updates');
        emit('error', 'Unable to fetch progress updates. Please refresh the page.');
        stopPolling();
      }
    }
  } finally {
    isLoading.value = false;
  }
};

const startPolling = () => {
  if (pollInterval.value) {
    clearInterval(pollInterval.value);
  }

  // Immediate initial fetch for instant display (within 100ms)
  fetchProgress();

  // Enhanced polling with adaptive intervals for real-time updates
  let pollCount = 0;
  const poll = () => {
    fetchProgress();
    pollCount++;

    // Adaptive polling: more frequent initially, then slower
    let nextInterval = 2000; // 2 seconds default
    if (pollCount < 5) {
      nextInterval = 1000; // 1 second for first 5 polls
    } else if (pollCount > 30) {
      nextInterval = 5000; // 5 seconds after 30 polls
    }

    // Enhanced completion detection - check multiple conditions
    const shouldContinuePolling = !progress.value?.isComplete &&
                                  !progress.value?.hasError &&
                                  progress.value?.status !== 'completed' &&
                                  progress.value?.progressPercentage < 100;

    if (shouldContinuePolling) {
      pollInterval.value = window.setTimeout(poll, nextInterval);
    } else {
      // Analysis is complete, emit completion event
      console.log('Analysis completed, stopping polling');
      emit('complete', progress.value);
    }
  };

  // Start adaptive polling after initial fetch
  pollInterval.value = window.setTimeout(poll, 1000);
};

const stopPolling = () => {
  if (pollInterval.value) {
    clearInterval(pollInterval.value);
    pollInterval.value = null;
  }
};

const getStepIcon = (stepNumber: number) => {
  return stepIcons[stepNumber as keyof typeof stepIcons] || Clock;
};

const getStepStatusColor = (step: ProgressStep) => {
  if (step.isFailed) return 'text-red-500';
  if (step.isCompleted) return 'text-green-600';
  if (step.isActive) return 'text-blue-600 animate-pulse';
  return 'text-gray-400';
};

const getStepBgColor = (step: ProgressStep) => {
  if (step.isFailed) return 'bg-red-50 border-red-200 shadow-sm';
  if (step.isCompleted) return 'bg-gradient-to-r from-green-50 to-emerald-50 border-green-200 shadow-md';
  if (step.isActive) return 'bg-gradient-to-r from-blue-50 to-indigo-50 border-blue-300 shadow-lg ring-2 ring-blue-100';
  return 'bg-gray-50 border-gray-200';
};

const getStepTextColor = (step: ProgressStep) => {
  if (step.isFailed) return 'text-red-900';
  if (step.isCompleted) return 'text-green-900';
  if (step.isActive) return 'text-blue-900 font-semibold';
  return 'text-gray-700';
};

const getStepDescriptionColor = (step: ProgressStep) => {
  if (step.isFailed) return 'text-red-700';
  if (step.isCompleted) return 'text-green-700';
  if (step.isActive) return 'text-blue-700';
  return 'text-gray-600';
};

const formatTime = (timestamp: string) => {
  return new Date(timestamp).toLocaleTimeString();
};

const progressBarWidth = computed(() => {
  return progress.value ? `${progress.value.progressPercentage}%` : '0%';
});

const fadeOut = () => {
  isVisible.value = false;
};

// Function to smoothly hide all progress components when analysis completes
const hideAllProgressComponents = () => {
  // Hide this component
  fadeOut();

  // Hide any other progress components on the page
  const progressComponents = document.querySelectorAll('[data-progress-component]');
  progressComponents.forEach(component => {
    if (component instanceof HTMLElement) {
      component.style.transition = 'opacity 0.5s ease-in-out, transform 0.5s ease-in-out';
      component.style.opacity = '0';
      component.style.transform = 'translateY(-20px)';

      setTimeout(() => {
        component.style.display = 'none';
      }, 500);
    }
  });

  // Emit completion event
  setTimeout(() => {
    const completionEvent = new CustomEvent('all-progress-hidden', {
      detail: { analysisId: props.analysisId }
    });
    document.dispatchEvent(completionEvent);
  }, 500);
};

onMounted(() => {
  fetchProgress();
  startPolling();
});

onUnmounted(() => {
  stopPolling();
});

// Expose methods for parent component
defineExpose({
  fadeOut,
  hideAllProgressComponents
});
</script>

<template>
  <Transition
    name="fullscreen-fade"
    appear
    @after-leave="$emit('complete')"
  >
    <div 
      v-if="isVisible"
      class="fixed inset-0 bg-white z-50 flex items-center justify-center"
    >
      <div class="max-w-2xl w-full mx-auto px-6">
        <!-- Header -->
        <div class="text-center mb-12">
          <div class="w-20 h-20 bg-blue-600 rounded-full flex items-center justify-center mx-auto mb-6">
            <Loader2 class="w-10 h-10 text-white animate-spin" />
          </div>
          <h1 class="text-3xl font-bold text-gray-900 mb-4">Analyzing Your Website</h1>
          <p class="text-lg text-gray-600 mb-2">{{ websiteUrl }}</p>
          <p class="text-sm text-gray-500">
            Our AI is performing a comprehensive analysis of your website's conversion potential
          </p>
        </div>

        <!-- Progress Bar -->
        <div class="mb-8">
          <div class="flex justify-between items-center mb-2">
            <span class="text-sm font-medium text-gray-700">
              {{ progress?.statusMessage || 'Initializing analysis...' }}
            </span>
            <span class="text-sm text-gray-500">
              {{ progress?.completedSteps || 0 }}/{{ progress?.totalSteps || 9 }} steps
            </span>
          </div>
          <div class="w-full bg-gray-200 rounded-full h-3">
            <div 
              class="bg-gradient-to-r from-blue-500 to-blue-600 h-3 rounded-full transition-all duration-500 ease-out"
              :style="{ width: progressBarWidth }"
            ></div>
          </div>
          <div class="text-center mt-2">
            <span class="text-lg font-semibold text-blue-600">
              {{ Math.round(progress?.progressPercentage || 0) }}%
            </span>
          </div>
        </div>

        <!-- Current Step -->
        <div class="text-center mb-8">
          <div class="inline-flex items-center px-4 py-2 bg-blue-50 rounded-full">
            <Loader2 class="w-4 h-4 text-blue-500 animate-spin mr-2" />
            <span class="text-blue-700 font-medium">{{ progress?.statusMessage || 'Initializing...' }}</span>
          </div>

          <!-- Estimated Time Remaining -->
          <div class="mt-4 text-sm text-blue-600">
            <Clock class="w-4 h-4 inline mr-1" />
            {{ formatTimeRemaining(getEstimatedTimeRemaining(progress?.currentStep || 1)) }}
          </div>

          <!-- Real-time Status Indicator -->
          <div class="mt-2 flex items-center justify-center text-xs text-green-600">
            <div class="w-2 h-2 bg-green-500 rounded-full animate-pulse mr-2"></div>
            Live progress tracking active
          </div>
        </div>

        <!-- Steps List -->
        <div v-if="progress?.steps" class="space-y-4">
          <div
            v-for="step in progress.steps"
            :key="step.stepNumber"
            :class="getStepBgColor(step)"
            class="p-5 rounded-xl border transition-all duration-500 ease-in-out transform hover:scale-[1.02]"
          >
            <div class="flex items-center">
              <div class="flex-shrink-0 mr-4">
                <div class="relative">
                  <component
                    :is="getStepIcon(step.stepNumber)"
                    :class="getStepStatusColor(step)"
                    class="w-7 h-7 transition-all duration-300"
                  />
                  <!-- Animated ring for active step -->
                  <div
                    v-if="step.isActive"
                    class="absolute inset-0 rounded-full border-2 border-blue-400 animate-ping"
                  ></div>
                </div>
              </div>
              <div class="flex-grow">
                <div class="flex items-center justify-between">
                  <h3 :class="getStepTextColor(step)" class="font-semibold text-lg transition-colors duration-300">
                    {{ step.stepName }}
                  </h3>
                  <div class="flex items-center space-x-2">
                    <div v-if="step.isCompleted" class="flex items-center space-x-1">
                      <CheckCircle class="w-6 h-6 text-green-500 animate-bounce" />
                      <span class="text-xs font-medium text-green-600 bg-green-100 px-2 py-1 rounded-full">
                        Complete
                      </span>
                    </div>
                    <div v-else-if="step.isActive" class="flex items-center space-x-1">
                      <Loader2 class="w-6 h-6 text-blue-500 animate-spin" />
                      <span class="text-xs font-medium text-blue-600 bg-blue-100 px-2 py-1 rounded-full animate-pulse">
                        Processing
                      </span>
                    </div>
                    <div v-else-if="step.isFailed" class="flex items-center space-x-1">
                      <AlertCircle class="w-6 h-6 text-red-500" />
                      <span class="text-xs font-medium text-red-600 bg-red-100 px-2 py-1 rounded-full">
                        Failed
                      </span>
                    </div>
                    <div v-else class="flex items-center space-x-1">
                      <Clock class="w-5 h-5 text-gray-400" />
                      <span class="text-xs font-medium text-gray-500 bg-gray-100 px-2 py-1 rounded-full">
                        Pending
                      </span>
                    </div>
                  </div>
                </div>
                <p :class="getStepDescriptionColor(step)" class="text-sm mt-2 transition-colors duration-300">
                  {{ step.description }}
                </p>

                <div v-if="step.startedAt || step.completedAt" class="flex items-center space-x-4 mt-3 text-xs">
                  <span v-if="step.startedAt" class="text-gray-500 bg-gray-100 px-2 py-1 rounded">
                    Started: {{ formatTime(step.startedAt) }}
                  </span>
                  <span v-if="step.completedAt" class="text-green-600 bg-green-100 px-2 py-1 rounded">
                    Completed: {{ formatTime(step.completedAt) }}
                  </span>
                </div>

                <div v-if="step.errorMessage" class="mt-3 p-3 bg-red-50 border border-red-200 rounded-lg">
                  <div class="flex items-center">
                    <AlertCircle class="w-4 h-4 text-red-500 mr-2" />
                    <span class="text-sm font-medium text-red-800">Error Details:</span>
                  </div>
                  <p class="text-sm text-red-700 mt-1">{{ step.errorMessage }}</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Error State -->
        <div v-if="error" class="text-center mt-8">
          <div class="inline-flex items-center px-4 py-2 bg-red-50 rounded-full">
            <AlertCircle class="w-4 h-4 text-red-500 mr-2" />
            <span class="text-red-700 font-medium">{{ error }}</span>
          </div>
        </div>

        <!-- Loading State -->
        <div v-if="isLoading && !progress" class="text-center mt-8">
          <div class="inline-flex items-center px-4 py-2 bg-gray-50 rounded-full">
            <Loader2 class="w-4 h-4 text-gray-500 animate-spin mr-2" />
            <span class="text-gray-700 font-medium">Loading progress...</span>
          </div>
        </div>
      </div>
    </div>
  </Transition>
</template>

<style scoped>
.fullscreen-fade-enter-active,
.fullscreen-fade-leave-active {
  transition: opacity 0.5s ease-in-out;
}

.fullscreen-fade-enter-from,
.fullscreen-fade-leave-to {
  opacity: 0;
}
</style>
