<script setup lang="ts">
import { ref, onMounted, onUnmounted, computed } from 'vue';
import { CheckCir<PERSON>, Clock, AlertCircle, Loader2, Zap, FileText, BarChart3, Users, Search, MessageCircle } from 'lucide-vue-next';

interface ProgressStep {
  stepNumber: number;
  stepName: string;
  description: string;
  status: 'pending' | 'in_progress' | 'completed' | 'failed';
  startedAt?: string;
  completedAt?: string;
  errorMessage?: string;
  isActive: boolean;
  isCompleted: boolean;
  isFailed: boolean;
}

interface AnalysisProgress {
  analysisId: string;
  status: 'pending' | 'in_progress' | 'completed' | 'failed';
  statusMessage: string;
  currentStep: string;
  progressPercentage: number;
  completedSteps: number;
  totalSteps: number;
  isComplete: boolean;
  hasError: boolean;
  startedAt?: string;
  completedAt?: string;
  errorMessage?: string;
  steps: ProgressStep[];
  contentStatus: {
    pageSummary: boolean;
    prosAndCons: boolean;
    leadInsights: boolean;
    performanceInsights: boolean;
    seoInsights: boolean;
    chatContext: boolean;
    allContent: boolean;
  };
  lastUpdated: string;
}

const props = defineProps<{
  analysisId: string;
  onComplete?: () => void;
}>();

const progress = ref<AnalysisProgress | null>(null);
const isLoading = ref(true);
const error = ref<string | null>(null);
const pollInterval = ref<number | null>(null);

// Step icons mapping
const stepIcons = {
  1: FileText,
  2: FileText,
  3: BarChart3,
  4: Users,
  5: Zap,
  6: Search,
  7: MessageCircle,
  8: CheckCircle
};

const fetchProgress = async () => {
  try {
    const response = await fetch(`/api/analysis-progress?analysisId=${props.analysisId}`);
    
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }
    
    const data = await response.json();
    
    if (data.error) {
      throw new Error(data.error);
    }
    
    progress.value = data;
    error.value = null;
    
    // If analysis is complete or failed, stop polling and notify parent
    if (data.isComplete || data.hasError) {
      stopPolling();
      if (data.isComplete && props.onComplete) {
        props.onComplete();
      }
    }
  } catch (err) {
    console.error('Error fetching progress:', err);
    error.value = err instanceof Error ? err.message : 'Failed to fetch progress';
    
    // Stop polling on persistent errors
    if (error.value.includes('404') || error.value.includes('403')) {
      stopPolling();
    }
  } finally {
    isLoading.value = false;
  }
};

const startPolling = () => {
  // Poll every 2 seconds for real-time updates
  pollInterval.value = window.setInterval(fetchProgress, 2000);
};

const stopPolling = () => {
  if (pollInterval.value) {
    clearInterval(pollInterval.value);
    pollInterval.value = null;
  }
};

const getStepIcon = (stepNumber: number) => {
  return stepIcons[stepNumber as keyof typeof stepIcons] || Clock;
};

const getStepStatusColor = (step: ProgressStep) => {
  if (step.isFailed) return 'text-red-500';
  if (step.isCompleted) return 'text-green-500';
  if (step.isActive) return 'text-blue-500';
  return 'text-gray-400';
};

const getStepBgColor = (step: ProgressStep) => {
  if (step.isFailed) return 'bg-red-50 border-red-200';
  if (step.isCompleted) return 'bg-green-50 border-green-200';
  if (step.isActive) return 'bg-blue-50 border-blue-200';
  return 'bg-gray-50 border-gray-200';
};

const formatTime = (timestamp: string) => {
  return new Date(timestamp).toLocaleTimeString();
};

const progressBarColor = computed(() => {
  if (progress.value?.hasError) return 'bg-red-500';
  if (progress.value?.isComplete) return 'bg-green-500';
  return 'bg-blue-500';
});

onMounted(() => {
  fetchProgress();
  startPolling();
});

onUnmounted(() => {
  stopPolling();
});
</script>

<template>
  <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
    <div class="mb-6">
      <div class="flex items-center justify-between mb-4">
        <h3 class="text-lg font-semibold text-gray-900">Analysis Progress</h3>
        <div v-if="progress && !progress.isComplete && !progress.hasError" class="flex items-center text-sm text-gray-500">
          <Loader2 class="w-4 h-4 mr-2 animate-spin" />
          Processing...
        </div>
      </div>

      <!-- Progress Bar -->
      <div v-if="progress" class="mb-4">
        <div class="flex items-center justify-between mb-2">
          <span class="text-sm font-medium text-gray-700">{{ progress.statusMessage }}</span>
          <span class="text-sm text-gray-500">{{ progress.progressPercentage }}%</span>
        </div>
        <div class="w-full bg-gray-200 rounded-full h-2">
          <div 
            :class="progressBarColor"
            class="h-2 rounded-full transition-all duration-500 ease-out"
            :style="{ width: `${progress.progressPercentage}%` }"
          ></div>
        </div>
      </div>

      <!-- Error State -->
      <div v-if="error || (progress && progress.hasError)" class="mb-4 p-4 bg-red-50 border border-red-200 rounded-lg">
        <div class="flex items-center">
          <AlertCircle class="w-5 h-5 text-red-500 mr-2" />
          <span class="text-red-700 font-medium">Analysis Failed</span>
        </div>
        <p class="text-red-600 text-sm mt-1">
          {{ error || progress?.errorMessage || 'An error occurred during analysis' }}
        </p>
      </div>

      <!-- Success State -->
      <div v-if="progress && progress.isComplete" class="mb-4 p-4 bg-green-50 border border-green-200 rounded-lg">
        <div class="flex items-center">
          <CheckCircle class="w-5 h-5 text-green-500 mr-2" />
          <span class="text-green-700 font-medium">Analysis Complete!</span>
        </div>
        <p class="text-green-600 text-sm mt-1">
          All content has been generated and is ready for viewing.
        </p>
      </div>
    </div>

    <!-- Progress Steps -->
    <div v-if="progress && progress.steps" class="space-y-3">
      <h4 class="text-sm font-medium text-gray-700 mb-3">Generation Steps</h4>
      
      <div 
        v-for="step in progress.steps" 
        :key="step.stepNumber"
        :class="getStepBgColor(step)"
        class="p-3 rounded-lg border transition-all duration-200"
      >
        <div class="flex items-start">
          <div class="flex-shrink-0 mr-3">
            <component 
              :is="getStepIcon(step.stepNumber)"
              :class="getStepStatusColor(step)"
              class="w-5 h-5"
            />
          </div>
          
          <div class="flex-1 min-w-0">
            <div class="flex items-center justify-between">
              <h5 class="text-sm font-medium text-gray-900">{{ step.stepName }}</h5>
              <div class="flex items-center space-x-2">
                <span v-if="step.isActive" class="flex items-center text-xs text-blue-600">
                  <Loader2 class="w-3 h-3 mr-1 animate-spin" />
                  In Progress
                </span>
                <span v-else-if="step.isCompleted" class="text-xs text-green-600 font-medium">
                  Completed
                </span>
                <span v-else-if="step.isFailed" class="text-xs text-red-600 font-medium">
                  Failed
                </span>
                <span v-else class="text-xs text-gray-500">
                  Pending
                </span>
              </div>
            </div>
            
            <p class="text-xs text-gray-600 mt-1">{{ step.description }}</p>
            
            <div v-if="step.startedAt || step.completedAt" class="flex items-center space-x-4 mt-2 text-xs text-gray-500">
              <span v-if="step.startedAt">Started: {{ formatTime(step.startedAt) }}</span>
              <span v-if="step.completedAt">Completed: {{ formatTime(step.completedAt) }}</span>
            </div>
            
            <div v-if="step.errorMessage" class="mt-2 text-xs text-red-600">
              Error: {{ step.errorMessage }}
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Loading State -->
    <div v-if="isLoading" class="flex items-center justify-center py-8">
      <Loader2 class="w-6 h-6 animate-spin text-blue-500 mr-2" />
      <span class="text-gray-600">Loading progress...</span>
    </div>
  </div>
</template>

<style scoped>
.animate-spin {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
</style>
