<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { supabase } from '../../lib/supabase';
import type { Database } from '../../types/supabase';
import { 
  Lightbulb, 
  AlertCircle, 
  TrendingUp, 
  Clock,
  Info
} from 'lucide-vue-next';
import SuggestionCarousel from './SuggestionCarousel.vue';

const props = defineProps<{
  analysis: Database['public']['Tables']['analyses']['Row'];
}>();

type Suggestion = Database['public']['Tables']['suggestions']['Row'];

const suggestions = ref<Suggestion[]>([]);
const loading = ref(true);

onMounted(async () => {
  await loadSuggestions();

  // Listen for refresh events from parent component
  document.addEventListener('refresh-tab-data', (event: Event) => {
    const customEvent = event as CustomEvent;
    if (customEvent.detail?.analysisId === props.analysis.id) {
      console.log('Refreshing suggestions data...');
      loadSuggestions();
    }
  });
});

const loadSuggestions = async () => {
  try {
    // First, get all suggestions for this analysis
    const { data, error } = await supabase
      .from('suggestions')
      .select(`
        *,
        suggestion_headers(ai_generated_title)
      `)
      .eq('analysis_id', props.analysis.id)
      .order('priority', { ascending: true });

    if (error) throw error;

    if (!data || data.length === 0) {
      // No suggestions found - they might still be generating
      suggestions.value = [];
      loading.value = false;
      return;
    }

    suggestions.value = data;

    // Check if any suggestions need headers
    const suggestionsNeedingHeaders = suggestions.value.filter(
      s => !s.suggestion_headers || s.suggestion_headers.length === 0
    );

    if (suggestionsNeedingHeaders.length > 0) {
      await generateMissingHeaders();
    }
  } catch (error) {
    console.error('Error loading suggestions:', error);
  } finally {
    loading.value = false;
  }
};

const generateMissingHeaders = async () => {
  const suggestionsNeedingHeaders = suggestions.value.filter(
    s => !s.suggestion_headers || s.suggestion_headers.length === 0
  );

  if (suggestionsNeedingHeaders.length === 0) return;

  try {
    const response = await fetch('/api/generate-suggestion-headers', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        suggestions: suggestionsNeedingHeaders.map(s => ({
          id: s.id,
          title: s.title,
          description: s.description,
          category: s.category
        }))
      })
    });

    if (response.ok) {
      // Reload suggestions to get the new headers
      const { data, error } = await supabase
        .from('suggestions')
        .select(`
          *,
          suggestion_headers(ai_generated_title)
        `)
        .eq('analysis_id', props.analysis.id)
        .order('priority', { ascending: true });

      if (!error && data) {
        suggestions.value = data;
      }
    }
  } catch (error) {
    console.error('Error generating suggestion headers:', error);
  }
};

const groupedSuggestions = computed(() => {
  const groups: Record<string, Suggestion[]> = {};
  suggestions.value.forEach(suggestion => {
    const category = suggestion.category || 'General';
    if (!groups[category]) {
      groups[category] = [];
    }
    groups[category].push(suggestion);
  });
  return groups;
});

const getCategoryIcon = (category: string) => {
  switch (category.toLowerCase()) {
    case 'conversion': return TrendingUp;
    case 'performance': return Clock;
    case 'seo': return Lightbulb;
    default: return Lightbulb;
  }
};

const getImpactColor = (impact: string) => {
  switch (impact.toLowerCase()) {
    case 'high': return 'bg-red-100 text-red-800 border-red-200';
    case 'medium': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
    case 'low': return 'bg-green-100 text-green-800 border-green-200';
    default: return 'bg-gray-100 text-gray-800 border-gray-200';
  }
};

const getEffortColor = (effort: string) => {
  switch (effort.toLowerCase()) {
    case 'high': return 'text-red-600';
    case 'medium': return 'text-yellow-600';
    case 'low': return 'text-green-600';
    default: return 'text-gray-600';
  }
};

const getImpactIcon = (impact: string) => {
  switch (impact.toLowerCase()) {
    case 'high': return AlertCircle;
    case 'medium': return TrendingUp;
    case 'low': return Info;
    default: return Info;
  }
};

const getSuggestionHeader = (suggestion: any) => {
  if (suggestion.suggestion_headers && suggestion.suggestion_headers.length > 0) {
    return suggestion.suggestion_headers[0].ai_generated_title;
  }
  return suggestion.title;
};

</script>

<template>
  <div class="space-y-6">
    <!-- Header -->
    <div class="flex items-center justify-between">
      <div>
        <div class="flex items-center mb-2">
          <Lightbulb class="w-5 h-5 text-gray-400 mr-2" />
          <h2 class="text-lg font-semibold text-gray-900">AI Recommendations</h2>
        </div>
        <p class="text-gray-600">Actionable insights to improve your conversion rate</p>
      </div>
      <div class="text-sm text-gray-500">
        {{ suggestions.length }} suggestions found
      </div>
    </div>

    <!-- Loading State -->
    <div v-if="loading" class="text-center py-12">
      <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
      <p class="text-gray-500">Loading suggestions...</p>
    </div>

    <!-- Empty State -->
    <div v-else-if="suggestions.length === 0" class="text-center py-12">
      <Lightbulb class="w-12 h-12 text-gray-400 mx-auto mb-4" />
      <h3 class="text-lg font-medium text-gray-900 mb-2">No suggestions yet</h3>
      <p class="text-gray-500">Analysis is still processing or no suggestions were generated.</p>
    </div>

    <!-- Suggestions List -->
    <div v-else class="space-y-8">
      <div v-for="(categoryGroup, category) in groupedSuggestions" :key="category" class="space-y-4">
        <!-- Category Header -->
        <div class="flex items-center space-x-3 pb-2 border-b border-gray-200">
          <component :is="getCategoryIcon(category)" class="w-5 h-5 text-gray-500" />
          <h3 class="text-lg font-medium text-gray-900">{{ category }} Suggestions</h3>
          <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-700">
            {{ categoryGroup.length }}
          </span>
        </div>

        <!-- Category Suggestions -->
        <div v-if="categoryGroup.length > 1">
          <SuggestionCarousel :suggestions="categoryGroup" />
        </div>
        <div v-else-if="categoryGroup.length === 1" class="p-4">
            <div class="border border-gray-200 rounded-lg p-6 flex flex-col">
                <div class="flex items-start justify-between mb-4">
                    <div class="flex-1">
                        <div class="flex items-center mb-3">
                            <span 
                                class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium border mr-3"
                                :class="getImpactColor(categoryGroup[0].impact_level)"
                            >
                                <component :is="getImpactIcon(categoryGroup[0].impact_level)" class="w-3 h-3 mr-1" />
                                {{ categoryGroup[0].impact_level }} Impact
                            </span>
                            <span 
                                class="text-xs font-medium"
                                :class="getEffortColor(categoryGroup[0].effort_level)"
                            >
                                {{ categoryGroup[0].effort_level }} Effort
                            </span>
                        </div>
                        <h4 class="text-lg font-medium text-gray-900 mb-2">
                            {{ getSuggestionHeader(categoryGroup[0]) }}
                        </h4>
                    </div>
                </div>
                <div class="text-gray-700 leading-relaxed mb-4 flex-grow">
                    <p>{{ categoryGroup[0].description }}</p>
                </div>
                <div v-if="categoryGroup[0].detailed_explanation" class="bg-blue-50 rounded-lg p-4 border border-blue-200 mt-auto">
                    <h5 class="font-medium text-blue-900 mb-2 flex items-center">
                        <Info class="w-4 h-4 mr-2" />
                        Implementation Tips
                    </h5>
                    <p class="text-blue-800 text-sm leading-relaxed">{{ categoryGroup[0].detailed_explanation }}</p>
                </div>
            </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.animate-spin {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
</style>
