-- Fix Progress System: Simplify to use analyses table only
-- Remove complex progress tracking tables and add simple fields

-- Add simplified progress fields to analyses table
ALTER TABLE public.analyses 
ADD COLUMN IF NOT EXISTS current_step integer DEFAULT 0,
ADD COLUMN IF NOT EXISTS is_completed boolean DEFAULT false;

-- Drop unused progress tables (if they exist)
DROP TABLE IF EXISTS public.progress_status_updates CASCADE;
DROP TABLE IF EXISTS public.live_progress_feed CASCADE;
DROP TABLE IF EXISTS public.analysis_generation_progress CASCADE;

-- Create index for faster progress queries
CREATE INDEX IF NOT EXISTS idx_analyses_progress ON public.analyses(current_step, is_completed);

-- Update RLS policies for new fields
ALTER POLICY "Users can view their own analyses" ON public.analyses USING (auth.uid() = user_id);
ALTER POLICY "Users can update their own analyses" ON public.analyses USING (auth.uid() = user_id);