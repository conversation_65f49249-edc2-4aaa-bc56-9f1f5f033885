import type { APIRoute } from 'astro';
import { supabaseAdmin } from '../../lib/supabase-client';
import { log } from '../../lib/logger';

export const prerender = false;

export const GET: APIRoute = async ({ request }) => {
  try {
    const url = new URL(request.url);
    const analysisId = url.searchParams.get('analysisId');

    if (!analysisId) {
      return new Response(JSON.stringify({ error: 'Missing analysisId parameter' }), {
        status: 400,
        headers: {
          'Content-Type': 'application/json',
          'Cache-Control': 'no-cache, no-store, must-revalidate',
          'Pragma': 'no-cache',
          'Expires': '0'
        }
      });
    }

    // Fetch analysis progress data with enhanced fields for real-time tracking
    const { data: analysis, error: analysisError } = await supabaseAdmin
      .from('analyses')
      .select(`
        id,
        generation_status,
        current_generation_step,
        completed_generation_steps,
        total_generation_steps,
        generation_started_at,
        generation_completed_at,
        generation_error,
        all_content_generated,
        pros_cons_generated,
        lead_insights_generated,
        performance_insights_generated,
        seo_insights_generated,
        chat_context_generated,
        page_summary_enhanced
      `)
      .eq('id', analysisId)
      .single();

    if (analysisError || !analysis) {
      log.error(`Failed to fetch analysis progress: ${analysisError?.message}`);
      return new Response(JSON.stringify({ error: 'Analysis not found' }), {
        status: 404,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Fetch detailed progress steps
    const { data: progressSteps, error: progressError } = await supabaseAdmin
      .from('analysis_generation_progress')
      .select('*')
      .eq('analysis_id', analysisId)
      .order('step_number');

    if (progressError) {
      log.error(`Failed to fetch progress steps: ${progressError.message}`);
    }

    // Fetch live progress feed for real-time updates
    const { data: liveProgressFeed, error: liveProgressError } = await supabaseAdmin
      .from('live_progress_feed')
      .select('*')
      .eq('analysis_id', analysisId)
      .order('step_number');

    if (liveProgressError) {
      log.warn(`Failed to fetch live progress feed: ${liveProgressError.message}`);
    }

    // Fetch progress status updates for detailed tracking
    const { data: progressStatusUpdates, error: statusUpdatesError } = await supabaseAdmin
      .from('progress_status_updates')
      .select(`
        *,
        progress_feed:progress_feed_id (
          current_step,
          status,
          progress_percentage
        )
      `)
      .in('progress_feed_id', liveProgressFeed?.map(feed => feed.id) || [])
      .order('created_at', { ascending: false })
      .limit(10);

    if (statusUpdatesError) {
      log.warn(`Failed to fetch progress status updates: ${statusUpdatesError.message}`);
    }

    // Enhanced progress calculation with live feed data
    let progressPercentage = 0;
    let currentStepNumber = 0;
    let totalSteps = analysis.total_generation_steps || 10;

    // Use live progress feed for more accurate progress if available
    if (liveProgressFeed && liveProgressFeed.length > 0) {
      const latestProgress = liveProgressFeed[liveProgressFeed.length - 1];
      progressPercentage = latestProgress.progress_percentage || 0;
      currentStepNumber = latestProgress.step_number || 0;
      totalSteps = latestProgress.total_steps || totalSteps;
    } else if (analysis.total_generation_steps > 0) {
      progressPercentage = Math.round((analysis.completed_generation_steps / analysis.total_generation_steps) * 100);
      currentStepNumber = analysis.completed_generation_steps;
    }

    // Determine current status message with enhanced real-time info
    let statusMessage = '';
    let isComplete = false;
    let hasError = false;
    let estimatedTimeRemaining = null;

    switch (analysis.generation_status) {
      case 'pending':
        statusMessage = 'Analysis queued for processing...';
        estimatedTimeRemaining = '2-3 minutes';
        break;
      case 'in_progress':
        if (liveProgressFeed && liveProgressFeed.length > 0) {
          const latestProgress = liveProgressFeed[liveProgressFeed.length - 1];
          statusMessage = latestProgress.status_message || analysis.current_generation_step || 'Processing analysis...';

          // Calculate estimated time remaining based on progress
          const remainingSteps = totalSteps - currentStepNumber;
          const avgTimePerStep = 15; // seconds
          const remainingSeconds = remainingSteps * avgTimePerStep;
          if (remainingSeconds > 60) {
            estimatedTimeRemaining = `${Math.ceil(remainingSeconds / 60)} minute(s)`;
          } else {
            estimatedTimeRemaining = `${remainingSeconds} seconds`;
          }
        } else {
          statusMessage = analysis.current_generation_step || 'Processing analysis...';
          estimatedTimeRemaining = '1-2 minutes';
        }
        break;
      case 'completed':
        statusMessage = 'Analysis completed successfully!';
        isComplete = true;
        progressPercentage = 100;
        break;
      case 'failed':
        statusMessage = analysis.generation_error || 'Analysis failed. Please try again.';
        hasError = true;
        break;
      default:
        statusMessage = 'Unknown status';
    }

    // Enhanced step formatting with live progress feed data
    const formattedSteps = progressSteps?.map(step => {
      // Find corresponding live progress feed entry
      const liveProgress = liveProgressFeed?.find(feed => feed.step_number === step.step_number);

      return {
        stepNumber: step.step_number,
        stepName: step.current_step,
        description: step.step_description || liveProgress?.detailed_message,
        status: step.status,
        startedAt: step.started_at || liveProgress?.started_at,
        completedAt: step.completed_at || liveProgress?.completed_at,
        errorMessage: step.error_message,
        isActive: step.status === 'in_progress',
        isCompleted: step.status === 'completed',
        isFailed: step.status === 'failed',
        progressPercentage: liveProgress?.progress_percentage || 0,
        statusMessage: liveProgress?.status_message,
        metadata: liveProgress?.metadata
      };
    }) || [];

    // Add any additional live progress steps not in the main progress table
    if (liveProgressFeed) {
      liveProgressFeed.forEach(liveStep => {
        if (!formattedSteps.find(step => step.stepNumber === liveStep.step_number)) {
          formattedSteps.push({
            stepNumber: liveStep.step_number,
            stepName: liveStep.current_step,
            description: liveStep.detailed_message,
            status: liveStep.status,
            startedAt: liveStep.started_at,
            completedAt: liveStep.completed_at,
            errorMessage: null,
            isActive: liveStep.status === 'in_progress',
            isCompleted: liveStep.status === 'completed',
            isFailed: liveStep.status === 'failed',
            progressPercentage: liveStep.progress_percentage || 0,
            statusMessage: liveStep.status_message,
            metadata: liveStep.metadata
          });
        }
      });
    }

    // Sort steps by step number
    formattedSteps.sort((a, b) => a.stepNumber - b.stepNumber);

    // Content generation status
    const contentStatus = {
      pageSummary: analysis.page_summary_enhanced,
      prosAndCons: analysis.pros_cons_generated,
      leadInsights: analysis.lead_insights_generated,
      performanceInsights: analysis.performance_insights_generated,
      seoInsights: analysis.seo_insights_generated,
      chatContext: analysis.chat_context_generated,
      allContent: analysis.all_content_generated
    };

    // Enhanced response with real-time progress tracking
    const response = {
      analysisId: analysis.id,
      status: analysis.generation_status,
      statusMessage,
      currentStep: analysis.current_generation_step,
      progressPercentage,
      completedSteps: analysis.completed_generation_steps,
      totalSteps: totalSteps,
      currentStepNumber,
      estimatedTimeRemaining,
      isComplete,
      hasError,
      startedAt: analysis.generation_started_at,
      completedAt: analysis.generation_completed_at,
      errorMessage: analysis.generation_error,
      steps: formattedSteps,
      contentStatus,
      lastUpdated: new Date().toISOString(),

      // Real-time progress tracking data
      liveProgress: {
        hasLiveFeed: !!liveProgressFeed && liveProgressFeed.length > 0,
        latestUpdate: liveProgressFeed && liveProgressFeed.length > 0
          ? liveProgressFeed[liveProgressFeed.length - 1].updated_at
          : null,
        recentStatusUpdates: progressStatusUpdates?.slice(0, 5) || [],
        feedCount: liveProgressFeed?.length || 0
      },

      // Performance metrics for debugging
      performance: {
        responseTime: Date.now(),
        dataFreshness: 'real-time',
        cacheStatus: 'no-cache'
      }
    };

    return new Response(JSON.stringify(response), {
      status: 200,
      headers: { 
        'Content-Type': 'application/json',
        'Cache-Control': 'no-cache, no-store, must-revalidate'
      }
    });

  } catch (error) {
    log.error(`Error fetching analysis progress: ${error}`);
    
    return new Response(JSON.stringify({
      error: 'Failed to fetch analysis progress',
      details: error instanceof Error ? error.message : 'Unknown error'
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
};

// POST endpoint for updating progress (for internal use)
export const POST: APIRoute = async ({ request }) => {
  try {
    const { analysisId, stepNumber, status, errorMessage, retryCount } = await request.json();

    if (!analysisId || !stepNumber || !status) {
      return new Response(JSON.stringify({ 
        error: 'Missing required parameters: analysisId, stepNumber, status' 
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Update progress directly in the table
    const updateData: any = {
      status: status,
      updated_at: new Date().toISOString()
    };

    if (status === 'in_progress') {
      updateData.started_at = new Date().toISOString();
    } else if (status === 'completed') {
      updateData.completed_at = new Date().toISOString();
    }

    if (errorMessage) {
      updateData.error_message = errorMessage;
    }

    if (retryCount !== undefined) {
      updateData.retry_count = retryCount;
    }

    const { error } = await supabaseAdmin
      .from('analysis_generation_progress')
      .update(updateData)
      .eq('analysis_id', analysisId)
      .eq('step_number', stepNumber);

    if (error) {
      throw error;
    }

    return new Response(JSON.stringify({ success: true }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });

  } catch (error) {
    log.error(`Error updating analysis progress: ${error}`);
    
    return new Response(JSON.stringify({
      error: 'Failed to update analysis progress',
      details: error instanceof Error ? error.message : 'Unknown error'
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
};
